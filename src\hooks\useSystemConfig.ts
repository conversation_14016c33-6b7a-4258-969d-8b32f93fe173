
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface SystemConfig {
  id: string;
  company_name: string;
  timezone: string;
  currency: string;
  auto_backup: boolean;
  theme: string;
  language: string;
  dashboard_layout: string;
}

export function useSystemConfig() {
  return useQuery({
    queryKey: ["systemConfig"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("system_config")
        .select("*")
        .single();

      if (error && error.code === 'PGRST116') {
        // No rows found, return default config
        return {
          id: '',
          company_name: "Worcester Bakstone",
          timezone: "South Africa Standard Time (UTC+2)",
          currency: "ZAR (South African Rand)",
          auto_backup: true,
          theme: "Light",
          language: "English",
          dashboard_layout: "Standard"
        } as SystemConfig;
      }

      if (error) throw new Error(error.message);
      return data as SystemConfig;
    }
  });
}

export function useUpdateSystemConfig() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (config: Partial<SystemConfig>) => {
      // First, try to get existing config
      const { data: existingConfig } = await supabase
        .from("system_config")
        .select("id")
        .single();

      if (existingConfig) {
        // Update existing record
        const { data, error } = await supabase
          .from("system_config")
          .update(config)
          .eq("id", existingConfig.id)
          .select()
          .single();

        if (error) throw new Error(error.message);
        return data;
      } else {
        // Create new record with default values
        const defaultConfig = {
          company_name: "Worcester Bakstone",
          timezone: "South Africa Standard Time (UTC+2)",
          currency: "ZAR (South African Rand)",
          auto_backup: true,
          theme: "Light",
          language: "English",
          dashboard_layout: "Standard",
          ...config
        };

        const { data, error } = await supabase
          .from("system_config")
          .insert(defaultConfig)
          .select()
          .single();

        if (error) throw new Error(error.message);
        return data;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["systemConfig"] });
    }
  });
}
