import React, { createContext, useContext, useEffect, useState } from "react";
import { useSystemConfig, useUpdateSystemConfig } from "@/hooks/useSystemConfig";

interface LanguageContextType {
  language: string;
  setLanguage: (language: string) => Promise<void>;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState("English");
  const { data: systemConfig } = useSystemConfig();
  const updateSystemConfigMutation = useUpdateSystemConfig();

  // Sync language from system config
  useEffect(() => {
    if (systemConfig?.language) {
      setLanguageState(systemConfig.language);
    }
  }, [systemConfig?.language]);

  const setLanguage = async (newLanguage: string) => {
    try {
      setLanguageState(newLanguage);
      await updateSystemConfigMutation.mutateAsync({ 
        language: newLanguage 
      });
    } catch (error) {
      console.error("Failed to update language:", error);
      // Revert on error
      if (systemConfig?.language) {
        setLanguageState(systemConfig.language);
      }
    }
  };

  const contextValue: LanguageContextType = {
    language,
    setLanguage,
    isLoading: updateSystemConfigMutation.isPending,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
