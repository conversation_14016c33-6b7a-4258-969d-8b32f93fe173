
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { User, Bell, Shield, Database, Palette } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { UserManagementSection } from "@/components/settings/UserManagementSection";
import { useNotificationSettings, useUpdateNotificationSettings } from "@/hooks/useNotificationSettings";
import { useSystemConfig, useUpdateSystemConfig } from "@/hooks/useSystemConfig";
import { useChangePassword } from "@/hooks/useUsers";
import { useAuth } from "@/contexts/AuthContext";
import { useAppTheme } from "@/hooks/useAppTheme";
import { useLanguage } from "@/contexts/LanguageContext";
import { useDashboardLayout } from "@/contexts/DashboardLayoutContext";

interface ProfileFormData {
  full_name: string;
  email: string;
  username: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export const SettingsPage = () => {
  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  const { currentUser } = useAuth();

  // If no user is authenticated, show a message
  if (!currentUser) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-lg text-slate-600">Please log in to access settings.</p>
        </div>
      </div>
    );
  }

  const { data: notificationSettings } = useNotificationSettings(currentUser.id);
  const { data: systemConfig } = useSystemConfig();
  const updateNotificationsMutation = useUpdateNotificationSettings();
  const updateSystemConfigMutation = useUpdateSystemConfig();
  const changePasswordMutation = useChangePassword();

  // New hooks for appearance & preferences
  const { displayTheme, setTheme: setAppTheme, isLoading: themeLoading } = useAppTheme();
  const { language, setLanguage, isLoading: languageLoading } = useLanguage();
  const { layout, setLayout, isLoading: layoutLoading } = useDashboardLayout();

  // Local state for system configuration to enable controlled components
  const [localSystemConfig, setLocalSystemConfig] = useState({
    company_name: "",
    timezone: "",
    currency: ""
  });

  // Update local state when systemConfig changes
  useEffect(() => {
    if (systemConfig) {
      setLocalSystemConfig({
        company_name: systemConfig.company_name || "Worcester Bakstone",
        timezone: systemConfig.timezone || "South Africa Standard Time (UTC+2)",
        currency: systemConfig.currency || "ZAR (South African Rand)"
      });
    }
  }, [systemConfig]);

  const profileForm = useForm<ProfileFormData>({
    defaultValues: {
      full_name: currentUser.full_name,
      email: currentUser.email || "",
      username: currentUser.username
    }
  });

  const passwordForm = useForm<PasswordFormData>();

  const onUpdateProfile = async (data: ProfileFormData) => {
    setIsUpdatingProfile(true);
    try {
      // In a real app, you would update the current user's profile
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success("Profile updated successfully");
    } catch (error) {
      toast.error("Failed to update profile");
    } finally {
      setIsUpdatingProfile(false);
    }
  };

  const onChangePassword = async (data: PasswordFormData) => {
    if (data.newPassword !== data.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    setIsChangingPassword(true);
    try {
      await changePasswordMutation.mutateAsync({
        userId: currentUser.id,
        currentPassword: data.currentPassword,
        newPassword: data.newPassword
      });
      toast.success("Password changed successfully");
      passwordForm.reset();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to change password");
    } finally {
      setIsChangingPassword(false);
    }
  };

  const updateNotificationSetting = async (key: string, value: boolean) => {
    try {
      await updateNotificationsMutation.mutateAsync({
        userId: currentUser.id,
        settings: { [key]: value }
      });
      toast.success("Notification settings updated");
    } catch (error) {
      toast.error("Failed to update notification settings");
    }
  };

  const updateSystemSetting = async (key: string, value: string | boolean) => {
    try {
      // Update local state immediately for better UX
      if (typeof value === "string" && ["company_name", "timezone", "currency"].includes(key)) {
        setLocalSystemConfig(prev => ({ ...prev, [key]: value }));
      }

      await updateSystemConfigMutation.mutateAsync({ [key]: value });
      toast.success(`${key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} updated successfully`);
    } catch (error) {
      console.error("Failed to update system configuration:", error);
      toast.error(`Failed to update ${key.replace('_', ' ')}`);

      // Revert local state on error
      if (systemConfig && typeof value === "string" && ["company_name", "timezone", "currency"].includes(key)) {
        setLocalSystemConfig(prev => ({
          ...prev,
          [key]: systemConfig[key as keyof typeof systemConfig] || prev[key as keyof typeof prev]
        }));
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">System Settings</h1>
          <p className="text-slate-600">Configure system preferences and user settings</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User size={20} />
              User Profile
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={profileForm.handleSubmit(onUpdateProfile)} className="space-y-4">
              <div>
                <label className="text-sm font-medium text-slate-700 mb-1 block">Full Name</label>
                <Input {...profileForm.register("full_name", { required: true })} />
              </div>
              <div>
                <label className="text-sm font-medium text-slate-700 mb-1 block">Email</label>
                <Input {...profileForm.register("email")} type="email" />
              </div>
              <div>
                <label className="text-sm font-medium text-slate-700 mb-1 block">Username</label>
                <Input {...profileForm.register("username", { required: true })} />
              </div>
              <Button 
                type="submit" 
                className="w-full"
                disabled={isUpdatingProfile}
              >
                {isUpdatingProfile ? "Updating..." : "Update Profile"}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell size={20} />
              Notification Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-slate-600">Receive email alerts</p>
              </div>
              <Switch 
                checked={notificationSettings?.email_notifications ?? true}
                onCheckedChange={(checked) => updateNotificationSetting("email_notifications", checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Production Alerts</p>
                <p className="text-sm text-slate-600">Critical production notifications</p>
              </div>
              <Switch 
                checked={notificationSettings?.production_alerts ?? true}
                onCheckedChange={(checked) => updateNotificationSetting("production_alerts", checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Fuel Level Warnings</p>
                <p className="text-sm text-slate-600">Low fuel level alerts</p>
              </div>
              <Switch 
                checked={notificationSettings?.fuel_level_warnings ?? true}
                onCheckedChange={(checked) => updateNotificationSetting("fuel_level_warnings", checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Employee Updates</p>
                <p className="text-sm text-slate-600">Staff schedule changes</p>
              </div>
              <Switch 
                checked={notificationSettings?.employee_updates ?? false}
                onCheckedChange={(checked) => updateNotificationSetting("employee_updates", checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <UserManagementSection />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield size={20} />
              Security Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={passwordForm.handleSubmit(onChangePassword)} className="space-y-4">
              <div>
                <label className="text-sm font-medium text-slate-700 mb-1 block">Current Password</label>
                <Input 
                  {...passwordForm.register("currentPassword", { required: true })}
                  type="password" 
                  placeholder="Enter current password" 
                />
              </div>
              <div>
                <label className="text-sm font-medium text-slate-700 mb-1 block">New Password</label>
                <Input 
                  {...passwordForm.register("newPassword", { 
                    required: true,
                    minLength: { value: 6, message: "Password must be at least 6 characters" }
                  })}
                  type="password" 
                  placeholder="Enter new password" 
                />
              </div>
              <div>
                <label className="text-sm font-medium text-slate-700 mb-1 block">Confirm Password</label>
                <Input 
                  {...passwordForm.register("confirmPassword", { required: true })}
                  type="password" 
                  placeholder="Confirm new password" 
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Two-Factor Authentication</p>
                  <p className="text-sm text-slate-600">Add extra security layer</p>
                </div>
                <Switch />
              </div>
              <Button 
                type="submit" 
                className="w-full"
                disabled={isChangingPassword}
              >
                {isChangingPassword ? "Updating..." : "Update Security"}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database size={20} />
              System Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-700 mb-1 block">Company Name</label>
              <Input
                value={localSystemConfig.company_name}
                onChange={(e) => setLocalSystemConfig(prev => ({ ...prev, company_name: e.target.value }))}
                onBlur={(e) => updateSystemSetting("company_name", e.target.value)}
                disabled={updateSystemConfigMutation.isPending}
                className="disabled:opacity-50"
              />
              {updateSystemConfigMutation.isPending && (
                <p className="text-xs text-slate-500 mt-1">Updating...</p>
              )}
            </div>
            <div>
              <label className="text-sm font-medium text-slate-700 mb-1 block">Time Zone</label>
              <select
                className="w-full p-2 border border-slate-300 rounded-md disabled:opacity-50"
                value={localSystemConfig.timezone}
                onChange={(e) => updateSystemSetting("timezone", e.target.value)}
                disabled={updateSystemConfigMutation.isPending}
              >
                <option value="South Africa Standard Time (UTC+2)">South Africa Standard Time (UTC+2)</option>
                <option value="UTC">UTC</option>
                <option value="Eastern Time (UTC-5)">Eastern Time (UTC-5)</option>
              </select>
              {updateSystemConfigMutation.isPending && (
                <p className="text-xs text-slate-500 mt-1">Updating...</p>
              )}
            </div>
            <div>
              <label className="text-sm font-medium text-slate-700 mb-1 block">Currency</label>
              <select
                className="w-full p-2 border border-slate-300 rounded-md disabled:opacity-50"
                value={localSystemConfig.currency}
                onChange={(e) => updateSystemSetting("currency", e.target.value)}
                disabled={updateSystemConfigMutation.isPending}
              >
                <option value="ZAR (South African Rand)">ZAR (South African Rand)</option>
                <option value="USD (US Dollar)">USD (US Dollar)</option>
                <option value="EUR (Euro)">EUR (Euro)</option>
              </select>
              {updateSystemConfigMutation.isPending && (
                <p className="text-xs text-slate-500 mt-1">Updating...</p>
              )}
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Auto Backup</p>
                <p className="text-sm text-slate-600">Daily system backups</p>
              </div>
              <Switch
                checked={systemConfig?.auto_backup ?? true}
                onCheckedChange={(checked) => updateSystemSetting("auto_backup", checked)}
                disabled={updateSystemConfigMutation.isPending}
              />
            </div>
            {updateSystemConfigMutation.isPending && (
              <div className="flex items-center gap-2 text-sm text-slate-500">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-slate-500"></div>
                Saving changes...
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette size={20} />
            Appearance & Preferences
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-slate-700 mb-2 block">Theme</label>
              <select
                className="w-full p-2 border border-slate-300 rounded-md disabled:opacity-50"
                value={displayTheme}
                onChange={(e) => setAppTheme(e.target.value.toLowerCase())}
                disabled={themeLoading}
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="auto">Auto</option>
              </select>
              {themeLoading && (
                <p className="text-xs text-slate-500 mt-1">Updating theme...</p>
              )}
            </div>
            <div>
              <label className="text-sm font-medium text-slate-700 mb-2 block">Language</label>
              <select
                className="w-full p-2 border border-slate-300 rounded-md disabled:opacity-50"
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                disabled={languageLoading}
              >
                <option value="English">English</option>
                <option value="Afrikaans">Afrikaans</option>
                <option value="Zulu">Zulu</option>
              </select>
              {languageLoading && (
                <p className="text-xs text-slate-500 mt-1">Updating language...</p>
              )}
            </div>
            <div>
              <label className="text-sm font-medium text-slate-700 mb-2 block">Dashboard Layout</label>
              <select
                className="w-full p-2 border border-slate-300 rounded-md disabled:opacity-50"
                value={layout}
                onChange={(e) => setLayout(e.target.value as any)}
                disabled={layoutLoading}
              >
                <option value="Compact">Compact</option>
                <option value="Standard">Standard</option>
                <option value="Detailed">Detailed</option>
              </select>
              {layoutLoading && (
                <p className="text-xs text-slate-500 mt-1">Updating layout...</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
