
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, UserCheck, Package, DollarSign } from "lucide-react";
import { TimeRange } from "./DashboardContent";
import { useDashboardMetrics } from "@/hooks/useDashboardMetrics";
import { useDashboardLayout } from "@/contexts/DashboardLayoutContext";
import { Skeleton } from "@/components/ui/skeleton";

interface KeyMetricsProps {
  timeRange: TimeRange;
}

export const KeyMetrics = ({ timeRange }: KeyMetricsProps) => {
  const { data: metrics, isLoading } = useDashboardMetrics(timeRange);
  const { getGridClassName } = useDashboardLayout();

  const metricCards = [
    {
      title: "Total Employees",
      value: metrics?.totalEmployees,
      icon: Users,
      subtext: "Current workforce size",
      footer: "Updated in real-time",
    },
    {
      title: "Active Employees",
      value: metrics?.activeEmployees,
      icon: User<PERSON><PERSON><PERSON>,
      subtext: "Employees currently active",
      footer: "Updated in real-time",
    },
    {
      title: `Total Pallets (${timeRange.charAt(0).toUpperCase() + timeRange.slice(1)})`,
      value: metrics?.totalPallets,
      icon: Package,
      subtext: `Pallets produced this ${timeRange}`,
      footer: "Updated in real-time",
    },
    {
      title: "Projected Payroll",
      value: metrics?.projectedPayroll,
      icon: DollarSign,
      subtext: "Estimated payroll amount",
      footer: "Based on recorded production",
      isCurrency: true,
    },
  ];

  return (
    <div className={`grid ${getGridClassName()}`}>
      {metricCards.map((metric, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-slate-600 flex items-center gap-2">
              <metric.icon size={16} />
              {metric.title}
            </CardTitle>
            <p className="text-xs text-slate-500">{metric.subtext}</p>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-9 w-24" />
            ) : (
              <div className="text-3xl font-bold text-slate-800">
                {metric.isCurrency ? `R${(metric.value ?? 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}` : (metric.value ?? 0).toLocaleString()}
              </div>
            )}
            <p className="text-sm text-slate-500 mt-1">{metric.footer}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
