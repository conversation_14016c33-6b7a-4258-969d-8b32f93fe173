import { useEffect } from "react";
import { useTheme } from "next-themes";
import { useSystemConfig, useUpdateSystemConfig } from "./useSystemConfig";

export function useAppTheme() {
  const { theme, setTheme, systemTheme } = useTheme();
  const { data: systemConfig } = useSystemConfig();
  const updateSystemConfigMutation = useUpdateSystemConfig();

  // Sync theme from system config on mount
  useEffect(() => {
    if (systemConfig?.theme && theme !== systemConfig.theme.toLowerCase()) {
      const configTheme = systemConfig.theme.toLowerCase();
      if (configTheme === "auto") {
        setTheme("system");
      } else {
        setTheme(configTheme);
      }
    }
  }, [systemConfig?.theme, theme, setTheme]);

  const updateTheme = async (newTheme: string) => {
    try {
      // Update next-themes
      if (newTheme === "auto") {
        setTheme("system");
      } else {
        setTheme(newTheme);
      }

      // Update system config
      const capitalizedTheme = newTheme.charAt(0).toUpperCase() + newTheme.slice(1);
      await updateSystemConfigMutation.mutateAsync({ 
        theme: capitalizedTheme 
      });
    } catch (error) {
      console.error("Failed to update theme:", error);
    }
  };

  const getCurrentTheme = () => {
    if (theme === "system") {
      return systemTheme || "light";
    }
    return theme || "light";
  };

  const getThemeDisplayValue = () => {
    if (theme === "system") {
      return "Auto";
    }
    return theme ? theme.charAt(0).toUpperCase() + theme.slice(1) : "Light";
  };

  return {
    theme: getCurrentTheme(),
    displayTheme: getThemeDisplayValue(),
    setTheme: updateTheme,
    isLoading: updateSystemConfigMutation.isPending,
  };
}
